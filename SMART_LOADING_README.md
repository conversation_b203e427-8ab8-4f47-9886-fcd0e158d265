# Full Context Chat Data Loading System (GPT-4o 128k)

## Overview

**UPDATED FOR GPT-4o 128k CONTEXT WINDOW**

The Full Context Chat Data Loading System now loads ALL conversation data simultaneously, taking advantage of GPT-4o's massive 128,000 token context window. No more filtering or limits - the AI has access to the complete conversation history for maximum insight and context.

## Features

### 🔍 **Keyword Extraction**
- **Stock Symbols**: Identifies 2-5 letter uppercase words (AAPL, TSLA, etc.)
- **Company Names**: Recognizes major companies (Spotify, Tesla, Apple, etc.)
- **Investment Terms**: Detects financial keywords (valuation, revenue, earnings, etc.)
- **Participants**: Matches known chat participants (<PERSON>, <PERSON>, etc.)
- **Market Sectors**: Identifies sector-related terms (tech, crypto, healthcare, etc.)

### 🎯 **Relevance Scoring**
- **Stock Symbol Matches**: 10 points (highest priority)
- **Company Name Matches**: 8 points
- **Investment Term Matches**: 3 points
- **Participant Matches**: 5 points
- **Sector Matches**: 4 points

### 📊 **Token Management**
- **Target Limit**: ~2500 tokens for conversation data
- **Total Context**: Stays under 3000 tokens including system prompt
- **Estimation**: ~4 characters per token for English text

### 🔄 **Fallback System**
- If no relevant conversations found, loads last 20 conversations
- Ensures the AI always has some context to work with

## Data Sources

The system loads from two conversation files:
- `Collective Chat.json` - Primary conversation data
- `Curation Collective.json` - Additional conversation data

## Usage

### Basic Usage
```javascript
// Load relevant conversations for a user query
const conversations = getRelevantConversations("What did Robin Maxwell say about Spotify?", 30);

// Load formatted conversations for AI context
const formattedData = loadCleanedConversations("Tell me about Tesla discussions");
```

### API Integration
The system is automatically integrated into the `/api/chat` endpoint:
```javascript
// User message is automatically passed to smart loading
const cleanedConversations = loadCleanedConversations(message);
```

## Example Queries and Results

### Query: "What did Robin Maxwell say about Spotify?"
**Keywords Extracted:**
- Stock Symbols: [SPOTIFY]
- Company Names: [spotify]
- Participants: [robin maxwell]

**Result:** 18 conversations, 1597 tokens, relevance scores: 23, 23, 18, 18...

### Query: "What stocks were discussed recently?"
**Keywords Extracted:**
- Investment Terms: [stock]

**Result:** 9 conversations, 559 tokens, relevance scores: 3, 3, 3...

### Query: "Tell me about crypto"
**Keywords Extracted:**
- Sectors: [crypto]

**Result:** Fallback to recent 20 conversations (no specific crypto matches found)

## Performance Benefits

### Before Smart Loading
- ❌ Loaded ALL conversations (~1000+ messages)
- ❌ Often exceeded token limits
- ❌ Irrelevant context diluted responses
- ❌ Higher API costs

### After Smart Loading
- ✅ Loads only relevant conversations (10-30 messages)
- ✅ Stays within token limits
- ✅ Highly targeted context improves response quality
- ✅ Reduced API costs

## Technical Implementation

### Functions
1. **`extractKeywords(userMessage)`** - Extracts relevant keywords from user input
2. **`calculateRelevanceScore(conversation, keywords)`** - Scores conversations based on keyword matches
3. **`estimateTokenCount(text)`** - Estimates token usage for text
4. **`getRelevantConversations(userMessage, maxMessages)`** - Main function that filters and returns relevant conversations
5. **`loadCleanedConversations(userMessage)`** - Updated function that uses smart loading

### Integration Points
- **Chat API**: Automatically uses smart loading for all user queries
- **System Prompt**: Dynamically includes only relevant conversation context
- **Token Management**: Ensures total context stays under limits

## Configuration

### Adjustable Parameters
- `maxMessages`: Maximum number of conversations to consider (default: 30)
- `maxTokens`: Maximum tokens for conversation data (default: 2500)
- `fallbackCount`: Number of recent conversations for fallback (default: 20)

### Scoring Weights
- Stock symbols: 10 points
- Company names: 8 points
- Participants: 5 points
- Sectors: 4 points
- Investment terms: 3 points

## Monitoring

The system provides detailed logging:
```
🔍 Extracted keywords: { stockSymbols: ['SPOTIFY'], companyNames: ['spotify'], ... }
✅ Selected 18 conversations (1597 estimated tokens)
🎯 Relevance scores: 23, 23, 18, 18, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5
```

## Future Enhancements

- **Semantic Search**: Use embeddings for better relevance matching
- **Time-based Weighting**: Prioritize more recent conversations
- **User Preference Learning**: Adapt to user's interests over time
- **Multi-language Support**: Extend keyword extraction to other languages
