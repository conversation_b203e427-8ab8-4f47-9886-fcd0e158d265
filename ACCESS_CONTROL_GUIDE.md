# 🔐 Simple Password Protection System

## Overview

A lightweight access control system has been added to your Echo Voice Leads application. This provides a simple password gate that controls who can access the application before they even reach the main authentication system.

## 🎯 **How It Works**

### **Two-Layer Security:**
1. **🔐 Access Password** - Simple password to enter the application
2. **🔑 User Authentication** - JWT-based login system for individual users

### **User Flow:**
```
User visits app → Access Password Required → Main App → User Login → Full Access
```

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Access Control Configuration
ACCESS_PASSWORD=Echo3423!
ACCESS_SESSION_TIMEOUT=86400000  # 24 hours in milliseconds
```

### **Default Settings**
- **Default Password**: `echo2025`
- **Session Timeout**: 24 hours
- **Session Storage**: In-memory (resets on server restart)

## 🌐 **User Experience**

### **Access Page Features**
- **Clean, professional interface** with Echo branding
- **Password input with validation**
- **Loading states and error handling**
- **Responsive design** for all devices
- **Security-focused messaging**

### **Access Flow**
1. User visits any URL (e.g., `https://yourdomain.com`)
2. Automatically redirected to `/access.html`
3. Enters access password
4. On success: redirected to main application
5. On failure: error message with retry option

## 🔒 **Security Features**

### **Session Management**
- **Secure session tokens** (32-byte random hex)
- **Automatic expiration** (24 hours default)
- **IP and User-Agent tracking**
- **Automatic cleanup** of expired sessions

### **Protection Scope**
- **All application routes** protected by default
- **API endpoints** return JSON errors when unauthorized
- **Static files** served only after access granted
- **Bypass paths** for access-related resources

### **Security Headers**
- **HttpOnly cookies** for session storage
- **Secure flag** in production
- **SameSite protection**
- **CSRF protection** via SameSite cookies

## 📱 **API Endpoints**

### **POST /api/access/verify**
Verify access password and create session.

**Request:**
```json
{
  "password": "echo2025"
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Access granted",
  "expiresIn": 86400000
}
```

**Error Response:**
```json
{
  "error": "Invalid access password",
  "timestamp": "2025-08-14T16:01:35.755Z"
}
```

### **GET /api/access/status**
Check current access status.

**Response:**
```json
{
  "hasAccess": true,
  "stats": {
    "activeSessions": 3,
    "sessionTimeout": 86400000,
    "hasCustomPassword": true
  },
  "timestamp": "2025-08-14T16:01:35.755Z"
}
```

### **POST /api/access/logout**
Revoke current access session.

**Response:**
```json
{
  "success": true,
  "message": "Access revoked"
}
```

## 🛠️ **Customization**

### **Change Access Password**
```bash
# Set in environment
export ACCESS_PASSWORD="your-secure-password"

# Or in .env file
ACCESS_PASSWORD=your-secure-password
```

### **Modify Session Timeout**
```bash
# 1 hour = 3600000 milliseconds
ACCESS_SESSION_TIMEOUT=3600000

# 1 week = 604800000 milliseconds
ACCESS_SESSION_TIMEOUT=604800000
```

### **Custom Branding**
Edit these files to customize the access page:
- `public/access.html` - Page structure and content
- `public/access.css` - Styling and branding
- `public/access.js` - Functionality and behavior

## 🔧 **Administration**

### **Monitor Active Sessions**
```bash
curl http://localhost:3000/api/access/status
```

### **Force Logout All Users**
Restart the server to clear all sessions (in-memory storage).

### **Check Access Logs**
Access attempts are logged in the application logs with anonymized data.

## 🚀 **Production Deployment**

### **Required Environment Variables**
```bash
# Production access password
ACCESS_PASSWORD=your-production-password-here

# Session timeout (24 hours recommended)
ACCESS_SESSION_TIMEOUT=86400000

# Enable production mode
NODE_ENV=production
```

### **Security Recommendations**
1. **Use a strong access password** (12+ characters)
2. **Change default password** immediately
3. **Set appropriate session timeout**
4. **Monitor access logs** for suspicious activity
5. **Use HTTPS** in production

### **Nginx Configuration**
```nginx
# Rate limiting for access attempts
location /api/access/ {
    limit_req zone=access burst=5 nodelay;
    proxy_pass http://localhost:3000;
}
```

## 🧪 **Testing**

### **Test Access Protection**
```bash
# Should redirect to access page
curl -I http://localhost:3000/

# Should require access password
curl http://localhost:3000/api/chat
```

### **Test Password Verification**
```bash
# Valid password
curl -X POST http://localhost:3000/api/access/verify \
  -H "Content-Type: application/json" \
  -d '{"password":"echo2025"}'

# Invalid password
curl -X POST http://localhost:3000/api/access/verify \
  -H "Content-Type: application/json" \
  -d '{"password":"wrong"}'
```

## 📊 **Benefits**

### **Simple & Effective**
- **Easy to implement** and maintain
- **No database required** (in-memory sessions)
- **Lightweight** with minimal performance impact
- **User-friendly** interface

### **Flexible Control**
- **Quick password changes** via environment variables
- **Configurable session timeouts**
- **Easy to disable** (remove middleware)
- **Customizable branding**

### **Security Enhancement**
- **Reduces attack surface** (blocks unauthorized access)
- **Prevents automated scanning** of protected endpoints
- **Adds extra layer** before main authentication
- **Session-based access** with automatic expiration

## 🎉 **Ready to Use!**

Your Echo Voice Leads application now has simple password protection:

1. **Default Password**: `echo2025`
2. **Access Page**: Automatically shown to unauthorized users
3. **Session Management**: 24-hour access sessions
4. **Production Ready**: Secure and configurable

**Change the default password before going live!** 🔒
