class AuthService {
    constructor() {
        // Simplified - no JWT authentication needed
        this.isAuthenticated = true; // Always authenticated after access control
    }

    async checkAuthStatus() {
        // Always return true since access control handles security
        return true;
    }

    async makeAuthenticatedRequest(url, options = {}) {
        // Set appropriate headers based on content type
        let headers = { ...options.headers };

        // For JSON requests, ensure Content-Type is set
        if (options.body && typeof options.body === 'string') {
            headers['Content-Type'] = 'application/json';
        }

        const response = await fetch(url, {
            ...options,
            headers,
            credentials: 'include'
        });

        return response;
    }
}

class VoiceLeadApp {
    constructor() {
        this.isRecording = false;
        this.isListening = false; // New: tracks if voice mode is active
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.currentAudio = null;
        this.leadData = {};
        this.conversationHistory = [];
        this.isFirstMessage = true;
        this.currentSessionId = null;
        this.allConversations = [];
        this.userHasInteracted = false;
        this.statusHideTimer = null;
        this.sessionId = Date.now().toString(); // Unique session ID
        this.voiceActivationTimeout = null; // For automatic recording restart
        this.authService = new AuthService(); // Add authentication service
        this.audioContextUnlocked = false; // Track if audio context is unlocked

        // Audio visualization properties
        this.audioContext = null;
        this.analyser = null;
        this.microphone = null;
        this.dataArray = null;
        this.animationId = null;
        this.audioVisualizationCanvas = null;
        this.canvasContext = null;

        this.initializeApp();
    }

    async initializeApp() {
        // Check authentication first
        const isAuthenticated = await this.authService.checkAuthStatus();

        this.initializeElements();
        this.bindEvents();
        this.checkMicrophoneSupport();
        this.checkAudioFormats();
        this.showWelcomeMessage();
        this.startStatusHideTimer();
    }

    initializeElements() {
        this.micButton = document.getElementById('micButton');
        this.micIcon = document.getElementById('micIcon');
        this.micStatus = document.getElementById('micStatus');
        this.statusIndicator = document.getElementById('statusIndicator');
        this.conversationDisplay = document.getElementById('conversationDisplay');
        this.playButton = document.getElementById('playButton');
        this.stopButton = document.getElementById('stopButton');
        this.oldStopButton = document.getElementById('oldStopButton');
        this.audioPlayer = document.getElementById('audioPlayer');
        this.saveLeadBtn = document.getElementById('saveLeadBtn');
        this.exportLeadBtn = document.getElementById('exportLeadBtn');
        this.leadInfo = document.getElementById('leadInfo');
        this.aiSpeakingIndicator = document.getElementById('aiSpeakingIndicator');
        this.aiThinkingIndicator = document.getElementById('aiThinkingIndicator');

        // Audio visualization elements
        this.audioVisualizationCanvas = document.getElementById('audioVisualization');
        if (this.audioVisualizationCanvas) {
            this.canvasContext = this.audioVisualizationCanvas.getContext('2d');
        }

        // Check if critical elements exist
        if (!this.micButton) {
            console.error('Microphone button element not found!');
            return;
        }
        if (!this.micIcon) {
            console.error('Microphone icon element not found!');
            return;
        }

        // Log successful initialization
        console.log('✅ Microphone button and icon elements found and initialized');
        console.log('Microphone button:', this.micButton);
        console.log('Microphone icon:', this.micIcon);

        // Diagnostic check for button visibility
        this.checkButtonVisibility();

        // Menu elements
        this.burgerMenu = document.getElementById('burgerMenu');
        this.sideMenu = document.getElementById('sideMenu');
        this.menuOverlay = document.getElementById('menuOverlay');
        this.closeMenu = document.getElementById('closeMenu');
        this.menuHistoryBtn = document.getElementById('menuHistoryBtn');
        this.menuExportBtn = document.getElementById('menuExportBtn');
        this.menuClearBtn = document.getElementById('menuClearBtn');

        // History elements
        this.historyModal = document.getElementById('historyModal');
        this.closeHistory = document.getElementById('closeHistory');
        this.historyContent = document.getElementById('historyContent');
    }

    bindEvents() {
        if (this.micButton) {
            this.micButton.addEventListener('click', () => this.toggleRecording());
        }
        if (this.playButton) {
            this.playButton.addEventListener('click', () => this.playLastResponse());
        }
        if (this.stopButton) {
            this.stopButton.addEventListener('click', () => this.stopEverything());
        }
        if (this.oldStopButton) {
            this.oldStopButton.addEventListener('click', () => this.stopAudio());
        }
        if (this.saveLeadBtn) {
            this.saveLeadBtn.addEventListener('click', () => this.saveLead());
        }
        if (this.exportLeadBtn) {
            this.exportLeadBtn.addEventListener('click', () => this.exportLead());
        }

        // Menu event listeners
        this.burgerMenu.addEventListener('click', () => this.toggleMenu());
        this.closeMenu.addEventListener('click', () => this.hideMenu());
        this.menuOverlay.addEventListener('click', () => this.hideMenu());
        this.menuHistoryBtn.addEventListener('click', () => {
            this.hideMenu();
            this.showHistory();
        });
        this.menuExportBtn.addEventListener('click', () => {
            this.hideMenu();
            this.exportConversationHistory();
        });
        this.menuClearBtn.addEventListener('click', () => {
            this.hideMenu();
            this.clearConversationHistory();
        });

        // History modal event listeners
        this.closeHistory.addEventListener('click', () => this.hideHistory());
        this.historyModal.addEventListener('click', (e) => {
            if (e.target === this.historyModal) {
                this.hideHistory();
            }
        });
    }

    checkButtonVisibility() {
        if (!this.micButton) return;

        const rect = this.micButton.getBoundingClientRect();
        const styles = window.getComputedStyle(this.micButton);

        console.log('🔍 Microphone Button Diagnostic:');
        console.log('- Position:', rect);
        console.log('- Display:', styles.display);
        console.log('- Visibility:', styles.visibility);
        console.log('- Opacity:', styles.opacity);
        console.log('- Z-index:', styles.zIndex);
        console.log('- Disabled:', this.micButton.disabled);
        console.log('- Parent element:', this.micButton.parentElement);

        // Check if button is in viewport
        const isInViewport = rect.top >= 0 && rect.left >= 0 &&
                           rect.bottom <= window.innerHeight &&
                           rect.right <= window.innerWidth;
        console.log('- In viewport:', isInViewport);

        // Check if button is actually visible
        const isVisible = rect.width > 0 && rect.height > 0 &&
                         styles.display !== 'none' &&
                         styles.visibility !== 'hidden' &&
                         parseFloat(styles.opacity) > 0;
        console.log('- Actually visible:', isVisible);
    }

    checkAudioFormats() {
        console.log('=== Audio Format Support Check ===');
        const formats = [
            'audio/webm',
            'audio/webm;codecs=opus',
            'audio/mp4',
            'audio/ogg',
            'audio/wav',
            'audio/mpeg'
        ];

        formats.forEach(format => {
            const supported = MediaRecorder.isTypeSupported(format);
            console.log(`${format}: ${supported ? '✓ Supported' : '✗ Not supported'}`);
        });
        console.log('=== End Audio Format Check ===');
    }

    async checkMicrophoneSupport() {
        console.log('🎤 Checking microphone support...');
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop());
            console.log('✅ Microphone access granted');
            this.updateStatus('Microphone ready - click to start');
        } catch (error) {
            console.error('❌ Microphone error:', error);
            if (this.micButton) {
                this.micButton.disabled = true;
                console.log('🔒 Microphone button disabled due to error');
            }
            console.error('Microphone error:', error);

            let errorMessage = 'Microphone access is required for voice functionality.';
            let details = error.message;

            if (error.name === 'NotAllowedError') {
                errorMessage = 'Microphone access was denied. Please allow microphone access and refresh the page.';
                details = 'Click the microphone icon in your browser\'s address bar to allow access.';
            } else if (error.name === 'NotFoundError') {
                errorMessage = 'No microphone found. Please connect a microphone and refresh the page.';
            }

            this.showError('Microphone Not Available', errorMessage, details);
        }
    }

    startStatusHideTimer() {
        // Hide status indicator after 5 seconds
        this.statusHideTimer = setTimeout(() => {
            this.statusIndicator.classList.add('hidden');
        }, 5000);
    }

    showWelcomeMessage() {
        // Show initial AI greeting (but don't auto-play audio)
        setTimeout(() => {
            const welcomeText = "Hello, James. The Market has been noisy - lets catch you up.";
            this.addMessage(welcomeText, 'ai');
            // Don't auto-play the welcome message - wait for user interaction
        }, 1000);
    }

    async toggleRecording() {
        // Mark that user has interacted with the page
        this.userHasInteracted = true;

        // Create audio context for auto-play (must be done in user interaction)
        this.prepareAudioContext();

        if (this.isListening) {
            // Turn OFF voice mode
            this.stopVoiceMode();
        } else {
            // Turn ON voice mode
            await this.startVoiceMode();
        }
    }

    prepareAudioContext() {
        // Create and prepare an audio element during user interaction
        // This ensures we have a "blessed" audio element that can auto-play
        if (!this.audioContextUnlocked) {
            // Create a reusable audio element in the user interaction context
            this.audioPlayer = document.getElementById('audioPlayer') || new Audio();

            // Play a silent sound to unlock audio context
            const silentAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            silentAudio.volume = 0;
            silentAudio.play().catch(() => {
                // Ignore errors for silent audio
            });

            this.audioContextUnlocked = true;
            console.log('Audio context unlocked for auto-play');
        }
    }

    prepareAudioForTTS() {
        // Create a "blessed" audio element during active user interaction
        // This element will be able to auto-play TTS responses
        this.ttsAudioElement = new Audio();
        this.ttsAudioElement.preload = 'auto';

        // Set up event handlers
        this.ttsAudioElement.addEventListener('ended', () => {
            if (this.isListening) {
                // If in listening mode, restart recording after AI finishes speaking
                this.updateUI('listening');
                this.showAIThinking('Listening...');
                this.voiceActivationTimeout = setTimeout(async () => {
                    if (this.isListening && !this.isRecording) {
                        await this.startRecording();
                    }
                }, 1000);
            } else {
                this.hideAIThinking();
                this.updateUI('ready');
            }
        });

        this.ttsAudioElement.addEventListener('error', (e) => {
            console.error('TTS Audio error:', e);
            this.handleAutoPlayBlocked();
        });

        console.log('🎵 TTS audio element prepared for auto-play');
    }

    async startVoiceMode() {
        this.isListening = true;
        this.updateUI('listening');
        this.updateStatus('Voice mode ON - Listening for speech...');

        // Show blob animation when voice mode starts
        this.showAIThinking('Listening...');

        // Start the first recording session
        await this.startRecording();
    }

    stopVoiceMode() {
        this.isListening = false;

        // Clear any pending restart timeout
        if (this.voiceActivationTimeout) {
            clearTimeout(this.voiceActivationTimeout);
            this.voiceActivationTimeout = null;
        }

        // Stop current recording if active
        if (this.isRecording) {
            this.stopRecording();
        }

        // Hide blob animation when voice mode stops
        this.hideAIThinking();

        this.updateUI('ready');
        this.updateStatus('Voice mode OFF - Click to activate');
    }

    async startRecording() {
        try {
            // Pre-create audio element for TTS response during user interaction
            this.prepareAudioForTTS();

            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 44100, // Changed to more compatible sample rate
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });

            // Try to use a supported audio format with better compatibility
            let options = {};
            let mimeType = '';

            // Check for supported formats in order of preference
            if (MediaRecorder.isTypeSupported('audio/webm')) {
                options = { mimeType: 'audio/webm' };
                mimeType = 'audio/webm';
            } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
                options = { mimeType: 'audio/mp4' };
                mimeType = 'audio/mp4';
            } else if (MediaRecorder.isTypeSupported('audio/ogg')) {
                options = { mimeType: 'audio/ogg' };
                mimeType = 'audio/ogg';
            } else {
                // Fallback to default (usually works)
                console.log('Using default MediaRecorder format');
                mimeType = 'audio/wav'; // Default assumption
            }

            console.log('Using audio format:', mimeType);

            this.mediaRecorder = new MediaRecorder(stream, options);
            this.audioChunks = [];
            this.recordedMimeType = mimeType; // Store for later use

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.processRecording();
            };

            this.mediaRecorder.start(1000); // Record in 1-second chunks
            this.isRecording = true;

            // Initialize and start audio visualization
            await this.connectMicrophoneToAnalyser(stream);
            this.startAudioVisualization();

            this.updateUI('recording');
            this.updateStatus('Listening... Click again to stop');

        } catch (error) {
            console.error('Recording error:', error);
            this.showError(
                'Recording Failed',
                'Could not start recording. Please check microphone permissions.',
                `Error: ${error.message}\nMake sure your browser has microphone access.`
            );

            // If in listening mode, try to restart after error
            if (this.isListening) {
                this.voiceActivationTimeout = setTimeout(async () => {
                    if (this.isListening && !this.isRecording) {
                        console.log('Retrying recording after error...');
                        await this.startRecording();
                    }
                }, 5000); // 5 second delay before retry
            }
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            this.isRecording = false;

            // Stop audio visualization
            this.stopAudioVisualization();

            this.updateUI('processing');
            this.updateStatus('Processing your message...');
        }
    }

    async processRecording() {
        try {
            // Check if we have audio data
            if (this.audioChunks.length === 0) {
                throw new Error('No audio data recorded');
            }

            // Determine the correct MIME type based on what was recorded
            let mimeType = this.recordedMimeType || 'audio/wav';
            if (this.mediaRecorder && this.mediaRecorder.mimeType) {
                mimeType = this.mediaRecorder.mimeType;
            }

            console.log('Processing audio with MIME type:', mimeType);
            console.log('Audio chunks:', this.audioChunks.length);

            const audioBlob = new Blob(this.audioChunks, { type: mimeType });

            // Check blob size
            if (audioBlob.size === 0) {
                throw new Error('Audio blob is empty');
            }

            console.log('Audio blob size:', audioBlob.size, 'bytes');

            // Send to speech-to-text API
            const transcription = await this.speechToText(audioBlob);

            if (transcription) {
                this.addMessage(transcription, 'user');

                // Send to AI for response
                const aiResponse = await this.getAIResponse(transcription);

                if (aiResponse) {
                    this.addMessage(aiResponse, 'ai');

                    // Convert AI response to speech
                    await this.textToSpeech(aiResponse);

                    // Extract lead information
                    this.extractLeadInfo(transcription, aiResponse);
                }
            }

            // If still in listening mode, restart recording after a brief pause
            if (this.isListening) {
                this.updateUI('listening');
                this.updateStatus('Voice mode ON - Listening for speech...');

                // Wait a moment before restarting recording to allow for AI response
                this.voiceActivationTimeout = setTimeout(async () => {
                    if (this.isListening && !this.isRecording) {
                        await this.startRecording();
                    }
                }, 2000); // 2 second delay
            } else {
                this.updateUI('ready');
                this.updateStatus('Ready for next message');
            }

        } catch (error) {
            console.error('Processing error:', error);
            this.showError(
                'Processing Failed',
                'Could not process your voice message. Please try again.',
                `Error: ${error.message}\nCheck your microphone and internet connection.`
            );

            // If still in listening mode, try to restart recording
            if (this.isListening) {
                this.updateUI('listening');
                this.voiceActivationTimeout = setTimeout(async () => {
                    if (this.isListening && !this.isRecording) {
                        await this.startRecording();
                    }
                }, 3000); // 3 second delay after error
            } else {
                this.updateUI('ready');
            }
        }
    }

    async speechToText(audioBlob) {
        try {
            // Update animation to show speech processing
            this.showAIThinking('Processing speech...');

            const formData = new FormData();

            // Determine file extension based on MIME type
            let filename = 'recording.wav';
            let mimeType = audioBlob.type;

            console.log('Audio blob MIME type:', mimeType);

            if (mimeType.includes('webm')) {
                filename = 'recording.webm';
            } else if (mimeType.includes('mp4')) {
                filename = 'recording.mp4';
            } else if (mimeType.includes('ogg')) {
                filename = 'recording.ogg';
            } else if (mimeType.includes('mpeg')) {
                filename = 'recording.mp3';
            } else {
                // Default to wav for unknown types
                filename = 'recording.wav';
            }

            console.log('Sending audio file:', filename, 'Size:', audioBlob.size);
            formData.append('audio', audioBlob, filename);

            const response = await this.authService.makeAuthenticatedRequest('/api/speech-to-text', {
                method: 'POST',
                body: formData
                // Don't set headers for FormData - let browser handle Content-Type with boundary
            });

            console.log('Speech-to-text response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Speech-to-text HTTP error:', errorText);
                throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            console.log('Speech-to-text result:', result);

            if (result.success && result.transcription) {
                console.log('Transcription successful:', result.transcription);
                return result.transcription;
            } else {
                throw new Error(result.error || result.details || 'Failed to transcribe audio');
            }

        } catch (error) {
            console.error('Speech-to-text error:', error);
            this.showError(
                'Speech Recognition Failed',
                'Could not convert your speech to text. Please try again.',
                `Error: ${error.message}\nThis might be due to audio format issues or network problems.`
            );
            return null;
        } finally {
            // Don't hide animation here - let it continue to AI response
        }
    }

    async getAIResponse(message) {
        try {
            // Update animation to show AI thinking
            this.showAIThinking('Sarah is thinking...');

            const response = await this.authService.makeAuthenticatedRequest('/api/chat', {
                method: 'POST',
                body: JSON.stringify({
                    message,
                    context: 'stock_business_lead',
                    leadData: this.leadData,
                    conversationHistory: this.conversationHistory,
                    sessionId: this.sessionId
                })
            });

            if (!response.ok) {
                console.error('AI response error:', response);
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.response) {
                // Add to conversation history
                this.conversationHistory.push(
                    { role: 'user', content: message },
                    { role: 'assistant', content: result.response }
                );

                // Keep conversation history manageable (last 10 exchanges)
                if (this.conversationHistory.length > 20) {
                    this.conversationHistory = this.conversationHistory.slice(-20);
                }

                return result.response;
            } else {
                throw new Error(result.error || 'Failed to get AI response');
            }

        } catch (error) {
            console.error('AI response error:', error);
            this.showError(
                'AI Response Failed',
                'Could not get a response from the AI. Please try again.',
                `Error: ${error.message}\nThis might be due to API issues or network problems.`
            );
            return 'Sorry, I encountered an error. Please try again.';
        } finally {
            // Don't hide animation here - let it continue to speech
        }
    }

    async textToSpeech(text) {
        try {
            // Update status to show speech generation
            this.updateStatus('Generating speech...', false);

            const response = await this.authService.makeAuthenticatedRequest('/api/text-to-speech', {
                method: 'POST',
                body: JSON.stringify({ text })
            });

            if (response.ok) {
                const audioBlob = await response.blob();
                const audioUrl = URL.createObjectURL(audioBlob);

                this.currentAudio = audioUrl;

                // Create a fresh audio element for this response
                const audioElement = new Audio();
                audioElement.src = audioUrl;
                audioElement.preload = 'auto';

                // Store references
                this.currentAudio = audioUrl;
                this.audioPlayer = audioElement;
                this.playButton.disabled = false;
                this.stopButton.disabled = false;

                // Set up event handlers for this audio element
                audioElement.addEventListener('ended', () => {
                    console.log('🎵 Audio playback ended');
                    if (this.isListening) {
                        // If in listening mode, restart recording after AI finishes speaking
                        this.updateUI('listening');
                        this.showAIThinking('Listening...');
                        this.voiceActivationTimeout = setTimeout(async () => {
                            if (this.isListening && !this.isRecording) {
                                await this.startRecording();
                            }
                        }, 1000);
                    } else {
                        this.hideAIThinking();
                        this.updateUI('ready');
                    }
                });

                audioElement.addEventListener('error', (e) => {
                    console.error('❌ Audio playback error:', e);
                    this.handleAutoPlayBlocked();
                });

                audioElement.addEventListener('loadstart', () => {
                    console.log('🎵 Audio loading started');
                });

                audioElement.addEventListener('canplay', () => {
                    console.log('🎵 Audio can start playing');
                });

                // Force auto-play - this should work since user just interacted
                try {
                    this.updateUI('speaking');
                    this.showAIThinking('Sarah is speaking...');

                    console.log('🎵 Attempting auto-play...');

                    // Try to play immediately
                    const playPromise = audioElement.play();

                    if (playPromise !== undefined) {
                        playPromise.then(() => {
                            console.log('✅ Audio auto-play successful!');
                        }).catch((error) => {
                            console.log('❌ Auto-play blocked:', error.name, error.message);
                            this.handleAutoPlayBlocked();
                        });
                    } else {
                        console.log('✅ Audio play() returned undefined - likely successful');
                    }
                } catch (error) {
                    console.log('❌ Audio setup error:', error.message);
                    this.handleAutoPlayBlocked();
                }



            } else {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

        } catch (error) {
            console.error('Text-to-speech error:', error);
            this.showError(
                'Speech Generation Failed',
                'Could not convert the AI response to speech, but you can still read the text.',
                `Error: ${error.message}\nThe conversation can continue without audio.`
            );
        }
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        messageDiv.textContent = text;

        // Remove welcome message if it exists
        const welcomeMessage = this.conversationDisplay.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        this.conversationDisplay.appendChild(messageDiv);
        this.conversationDisplay.scrollTop = this.conversationDisplay.scrollHeight;
    }

    extractLeadInfo(userMessage, aiResponse) {
        // Simple lead information extraction
        // In a real implementation, this would be more sophisticated
        // aiResponse could be used for more advanced extraction in the future
        
        const nameMatch = userMessage.match(/my name is (\w+)/i) || userMessage.match(/i'm (\w+)/i);
        if (nameMatch) {
            this.leadData.name = nameMatch[1];
        }
        
        const emailMatch = userMessage.match(/[\w.-]+@[\w.-]+\.\w+/);
        if (emailMatch) {
            this.leadData.email = emailMatch[0];
        }
        
        const phoneMatch = userMessage.match(/(\d{3}[-.]?\d{3}[-.]?\d{4})/);
        if (phoneMatch) {
            this.leadData.phone = phoneMatch[1];
        }
        
        // Update lead display
        this.updateLeadDisplay();
    }

    updateLeadDisplay() {
        if (Object.keys(this.leadData).length > 0) {
            let leadHtml = '<div class="lead-details">';
            
            if (this.leadData.name) {
                leadHtml += `<p><strong>Name:</strong> ${this.leadData.name}</p>`;
            }
            if (this.leadData.email) {
                leadHtml += `<p><strong>Email:</strong> ${this.leadData.email}</p>`;
            }
            if (this.leadData.phone) {
                leadHtml += `<p><strong>Phone:</strong> ${this.leadData.phone}</p>`;
            }
            
            leadHtml += '</div>';
            this.leadInfo.innerHTML = leadHtml;
            
            this.saveLeadBtn.disabled = false;
            this.exportLeadBtn.disabled = false;
        }
    }

    updateUI(state) {
        // Check if critical elements exist before updating UI
        if (!this.micButton || !this.micIcon) {
            console.error('Critical UI elements not found, cannot update UI');
            return;
        }

        switch (state) {
            case 'listening':
                this.micButton.classList.add('listening');
                this.micButton.classList.remove('recording');
                this.micIcon.className = 'fas fa-microphone-slash';
                if (this.micStatus) {
                    this.micStatus.textContent = 'Voice ON - Click to turn OFF';
                }
                if (this.stopButton) {
                    this.stopButton.style.display = 'flex';
                }
                this.hideAISpeaking(); // Hide blob animation
                break;
            case 'recording':
                this.micButton.classList.add('recording');
                this.micButton.classList.remove('listening');
                this.micIcon.className = 'fas fa-stop';
                if (this.micStatus) {
                    this.micStatus.textContent = 'Recording...';
                }
                if (this.stopButton) {
                    this.stopButton.style.display = 'flex';
                }
                this.hideAISpeaking(); // Hide blob animation
                break;
            case 'processing':
                this.micButton.classList.remove('recording', 'listening');
                this.micIcon.className = 'fas fa-spinner fa-spin';
                if (this.micStatus) {
                    this.micStatus.textContent = 'Processing...';
                }
                if (this.stopButton) {
                    this.stopButton.style.display = 'flex';
                }
                this.hideAISpeaking(); // Hide blob animation
                break;
            case 'speaking':
                this.micButton.classList.remove('recording');
                if (this.isListening) {
                    this.micButton.classList.add('listening');
                    this.micIcon.className = 'fas fa-microphone-slash';
                    if (this.micStatus) {
                        this.micStatus.textContent = 'Voice ON - AI speaking...';
                    }
                } else {
                    this.micIcon.className = 'fas fa-microphone';
                    if (this.micStatus) {
                        this.micStatus.textContent = 'AI is speaking...';
                    }
                }
                if (this.stopButton) {
                    this.stopButton.style.display = 'flex';
                }
                this.showAISpeaking(); // Use blob animation for speaking
                break;
            case 'ready':
                this.micButton.classList.remove('recording', 'listening');
                this.micIcon.className = 'fas fa-microphone';
                if (this.micStatus) {
                    this.micStatus.textContent = 'Click to turn voice ON';
                }
                if (this.stopButton) {
                    this.stopButton.style.display = 'none';
                }
                this.hideAISpeaking(); // Hide blob animation
                break;
        }
    }

    updateStatus(message, isError = false) {
        const statusText = this.statusIndicator.querySelector('.status-text');
        statusText.textContent = message;

        // Clear any existing hide timer
        if (this.statusHideTimer) {
            clearTimeout(this.statusHideTimer);
        }

        // Show the status indicator
        this.statusIndicator.classList.remove('hidden');

        // Add error styling if it's an error
        if (isError) {
            this.statusIndicator.style.background = 'rgba(220, 53, 69, 0.9)';
            statusText.style.color = '#ffffff';

            // Hide error status after 3 seconds
            this.statusHideTimer = setTimeout(() => {
                this.statusIndicator.classList.add('hidden');
            }, 3000);
        } else {
            this.statusIndicator.style.background = 'rgba(0, 0, 0, 0.7)';
            statusText.style.color = '#00d4aa';

            // Hide normal status after 2 seconds
            this.statusHideTimer = setTimeout(() => {
                this.statusIndicator.classList.add('hidden');
            }, 2000);
        }
    }

    showError(title, message, details = null) {
        // Create error message in conversation
        const errorDiv = document.createElement('div');
        errorDiv.className = 'message error-message';
        errorDiv.innerHTML = `
            <div class="error-content">
                <strong>❌ ${title}</strong>
                <p>${message}</p>
                ${details ? `<details><summary>Technical Details</summary><pre>${details}</pre></details>` : ''}
            </div>
        `;

        // Remove welcome message if it exists
        const welcomeMessage = this.conversationDisplay.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        this.conversationDisplay.appendChild(errorDiv);
        this.conversationDisplay.scrollTop = this.conversationDisplay.scrollHeight;

        // Also update status
        this.updateStatus(`Error: ${title}`, true);
    }

    handleAutoPlayBlocked() {
        // Show a visual indicator that audio is ready
        this.updateStatus('🔊 Audio ready - click to play', false);
        this.updateUI('ready');

        // Add a temporary play button to the latest AI message
        this.addPlayButtonToLatestMessage();

        // Also show a notification
        console.log('💡 Tip: Click the play button or enable auto-play in browser settings');
    }

    addPlayButtonToLatestMessage() {
        // Find the latest AI message and add a play button if it doesn't have one
        const messages = this.conversationDisplay.querySelectorAll('.ai-message');
        const latestMessage = messages[messages.length - 1];

        if (latestMessage && !latestMessage.querySelector('.play-audio-btn')) {
            const playButton = document.createElement('button');
            playButton.className = 'play-audio-btn';
            playButton.innerHTML = '<i class="fas fa-play"></i> Play Audio';
            playButton.style.cssText = `
                margin-left: 10px;
                padding: 5px 10px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            `;

            playButton.onclick = () => {
                this.playLastResponse();
                playButton.remove(); // Remove button after playing
            };

            latestMessage.appendChild(playButton);
        }
    }

    playLastResponse() {
        if (this.currentAudio) {
            // Show speaking animation when manually playing
            this.showAIThinking('Sarah is speaking...');
            this.audioPlayer.play();
            this.stopButton.disabled = false;
        }
    }

    stopAudio() {
        this.audioPlayer.pause();
        this.audioPlayer.currentTime = 0;
        this.oldStopButton.disabled = true;
    }

    stopEverything() {
        // Stop voice mode if active
        if (this.isListening) {
            this.stopVoiceMode();
        }

        // Stop recording if active
        if (this.isRecording) {
            this.stopRecording();
        }

        // Stop audio playback
        this.audioPlayer.pause();
        this.audioPlayer.currentTime = 0;

        // Reset UI to ready state
        this.updateUI('ready');
        this.updateStatus('Stopped');
    }

    async saveLead() {
        try {
            const response = await fetch('/api/save-lead', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.leadData)
            });
            
            if (response.ok) {
                this.updateStatus('Lead saved successfully');
            } else {
                this.updateStatus('Error saving lead');
            }
        } catch (error) {
            console.error('Save lead error:', error);
            this.updateStatus('Error saving lead');
        }
    }

    exportLead() {
        const leadText = JSON.stringify(this.leadData, null, 2);
        const blob = new Blob([leadText], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `lead_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // AI Animation Methods
    showAIThinking(message = 'Sarah is thinking...') {
        this.aiThinkingIndicator.style.display = 'flex';
        // Update the text message
        const thinkingText = this.aiThinkingIndicator.querySelector('.thinking-text');
        if (thinkingText) {
            thinkingText.textContent = message;
        }
        // Set state for styling
        if (message.includes('speaking')) {
            this.aiThinkingIndicator.setAttribute('data-state', 'speaking');
        } else {
            this.aiThinkingIndicator.setAttribute('data-state', 'thinking');
        }
    }

    hideAIThinking() {
        this.aiThinkingIndicator.style.display = 'none';
        // Reset to default message
        const thinkingText = this.aiThinkingIndicator.querySelector('.thinking-text');
        if (thinkingText) {
            thinkingText.textContent = 'Sarah is thinking...';
        }
    }

    showAISpeaking() {
        // Use the thinking animation for speaking as well
        this.showAIThinking('Sarah is speaking...');
    }

    hideAISpeaking() {
        this.hideAIThinking();
    }

    // Audio Visualization Methods
    async initializeAudioVisualization() {
        try {
            // Create audio context if it doesn't exist
            if (!this.audioContext) {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }

            // Create analyser node
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = 256;
            this.analyser.smoothingTimeConstant = 0.8;

            const bufferLength = this.analyser.frequencyBinCount;
            this.dataArray = new Uint8Array(bufferLength);

            return true;
        } catch (error) {
            console.error('Failed to initialize audio visualization:', error);
            return false;
        }
    }

    async connectMicrophoneToAnalyser(stream) {
        try {
            if (!this.audioContext || !this.analyser) {
                await this.initializeAudioVisualization();
            }

            // Connect microphone to analyser
            this.microphone = this.audioContext.createMediaStreamSource(stream);
            this.microphone.connect(this.analyser);

            return true;
        } catch (error) {
            console.error('Failed to connect microphone to analyser:', error);
            return false;
        }
    }

    startAudioVisualization() {
        if (!this.audioVisualizationCanvas || !this.canvasContext || !this.analyser) {
            return;
        }

        // Show the canvas
        this.audioVisualizationCanvas.style.display = 'block';
        this.audioVisualizationCanvas.classList.add('recording');

        // Start animation loop
        this.drawAudioVisualization();
    }

    stopAudioVisualization() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }

        if (this.audioVisualizationCanvas) {
            this.audioVisualizationCanvas.style.display = 'none';
            this.audioVisualizationCanvas.classList.remove('recording', 'listening');
        }

        // Disconnect microphone
        if (this.microphone) {
            this.microphone.disconnect();
            this.microphone = null;
        }
    }

    drawAudioVisualization() {
        if (!this.analyser || !this.dataArray || !this.canvasContext) {
            return;
        }

        this.animationId = requestAnimationFrame(() => this.drawAudioVisualization());

        // Get frequency data
        this.analyser.getByteFrequencyData(this.dataArray);

        const canvas = this.audioVisualizationCanvas;
        const ctx = this.canvasContext;
        const width = canvas.width;
        const height = canvas.height;
        const centerY = height / 2;

        // Clear canvas with subtle gradient background
        const bgGradient = ctx.createLinearGradient(0, 0, 0, height);
        bgGradient.addColorStop(0, 'rgba(0, 0, 0, 0.9)');
        bgGradient.addColorStop(0.5, 'rgba(0, 20, 40, 0.8)');
        bgGradient.addColorStop(1, 'rgba(0, 0, 0, 0.9)');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, width, height);

        // Calculate average volume for dynamic effects
        const average = this.dataArray.reduce((sum, value) => sum + value, 0) / this.dataArray.length;
        const normalizedAverage = average / 255;

        // Draw frequency bars (bottom half)
        const barCount = Math.min(32, this.dataArray.length);
        const barWidth = width / barCount;

        for (let i = 0; i < barCount; i++) {
            const dataIndex = Math.floor((i / barCount) * this.dataArray.length);
            const barHeight = (this.dataArray[dataIndex] / 255) * (height * 0.4);

            // Create dynamic gradient based on frequency
            const gradient = ctx.createLinearGradient(0, centerY, 0, centerY + barHeight);
            const intensity = this.dataArray[dataIndex] / 255;

            if (intensity > 0.7) {
                gradient.addColorStop(0, '#00d4aa');
                gradient.addColorStop(0.5, '#74d9e1');
                gradient.addColorStop(1, '#ffffff');
            } else if (intensity > 0.4) {
                gradient.addColorStop(0, '#007acc');
                gradient.addColorStop(1, '#00d4aa');
            } else {
                gradient.addColorStop(0, '#333333');
                gradient.addColorStop(1, '#007acc');
            }

            ctx.fillStyle = gradient;

            // Draw bars from center outward
            const x = i * barWidth;
            ctx.fillRect(x, centerY, barWidth - 2, barHeight);
            ctx.fillRect(x, centerY - barHeight, barWidth - 2, barHeight);
        }

        // Draw central waveform
        ctx.strokeStyle = `rgba(0, 212, 170, ${0.8 + normalizedAverage * 0.2})`;
        ctx.lineWidth = 2 + normalizedAverage * 2;
        ctx.beginPath();

        const waveformData = new Uint8Array(this.analyser.fftSize);
        this.analyser.getByteTimeDomainData(waveformData);

        const sliceWidth = width / waveformData.length;
        let x = 0;

        for (let i = 0; i < waveformData.length; i++) {
            const v = waveformData[i] / 128.0;
            const y = v * height / 4 + centerY;

            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }

            x += sliceWidth;
        }

        ctx.stroke();

        // Add dynamic glow effect based on volume
        if (normalizedAverage > 0.3) {
            ctx.shadowColor = '#00d4aa';
            ctx.shadowBlur = 10 + normalizedAverage * 20;
            ctx.strokeStyle = `rgba(0, 212, 170, ${normalizedAverage})`;
            ctx.lineWidth = 1;
            ctx.stroke();
            ctx.shadowBlur = 0;
        }

        // Draw volume level indicator
        this.drawVolumeIndicator(ctx, width, height, normalizedAverage);
    }

    drawVolumeIndicator(ctx, width, height, volume) {
        const indicatorWidth = width * 0.8;
        const indicatorHeight = 4;
        const x = (width - indicatorWidth) / 2;
        const y = height - 15;

        // Background
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.fillRect(x, y, indicatorWidth, indicatorHeight);

        // Volume level
        const volumeWidth = indicatorWidth * volume;
        const gradient = ctx.createLinearGradient(x, y, x + volumeWidth, y);

        if (volume > 0.8) {
            gradient.addColorStop(0, '#007acc');
            gradient.addColorStop(0.7, '#00d4aa');
            gradient.addColorStop(1, '#ffffff');
        } else if (volume > 0.5) {
            gradient.addColorStop(0, '#007acc');
            gradient.addColorStop(1, '#00d4aa');
        } else {
            gradient.addColorStop(0, '#333333');
            gradient.addColorStop(1, '#007acc');
        }

        ctx.fillStyle = gradient;
        ctx.fillRect(x, y, volumeWidth, indicatorHeight);
    }

    // Menu Methods
    toggleMenu() {
        if (this.sideMenu.classList.contains('active')) {
            this.hideMenu();
        } else {
            this.showMenu();
        }
    }

    showMenu() {
        this.sideMenu.classList.add('active');
        this.menuOverlay.classList.add('active');
        this.burgerMenu.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    hideMenu() {
        this.sideMenu.classList.remove('active');
        this.menuOverlay.classList.remove('active');
        this.burgerMenu.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling
    }

    // Conversation History Methods
    async showHistory() {
        this.historyModal.style.display = 'flex';
        this.historyContent.innerHTML = '<div class="loading">Loading conversations...</div>';

        try {
            const response = await this.authService.makeAuthenticatedRequest('/api/conversation-history');
            const data = await response.json();

            if (data.success && data.conversations && data.conversations.length > 0) {
                this.renderConversationHistory(data.conversations);
            } else {
                this.historyContent.innerHTML = '<div class="no-history">No conversation history found.</div>';
            }
        } catch (error) {
            console.error('Error loading conversation history:', error);
            this.historyContent.innerHTML = '<div class="no-history">Error loading conversation history.</div>';
        }
    }

    hideHistory() {
        this.historyModal.style.display = 'none';
    }

    renderConversationHistory(conversations) {
        // Group conversations by session or time proximity
        const groupedConversations = this.groupConversations(conversations);

        // Store for access by continue conversation function
        this.allConversations = groupedConversations;

        let html = '<div class="conversation-list">';

        groupedConversations.forEach(group => {
            const startDate = new Date(group.startTime);
            const formattedDate = startDate.toLocaleDateString();
            const formattedTime = startDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            html += `
                <div class="conversation-group" data-session-id="${group.sessionId}">
                    <div class="conversation-summary" onclick="app.toggleConversationDetails('${group.sessionId}')">
                        <div class="conversation-header">
                            <h4 class="conversation-title">${group.title}</h4>
                            <div class="conversation-meta">
                                <span class="conversation-date">${formattedDate} at ${formattedTime}</span>
                                <span class="message-count">${group.messages.length} messages</span>
                            </div>
                        </div>
                        <div class="conversation-preview">${this.escapeHtml(group.preview)}</div>
                        <div class="expand-icon">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                    <div class="conversation-details" id="details-${group.sessionId}" style="display: none;">
                        ${this.renderConversationMessages(group.messages)}
                        <div class="conversation-actions">
                            <button class="continue-btn" onclick="app.continueConversation('${group.sessionId}')">
                                <i class="fas fa-play"></i> Continue Conversation
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        this.historyContent.innerHTML = html;
    }

    groupConversations(conversations) {
        // Sort conversations by timestamp
        const sorted = conversations.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

        const groups = new Map();

        sorted.forEach(conv => {
            const sessionId = conv.sessionId || this.generateSessionFromTime(conv.timestamp);

            if (!groups.has(sessionId)) {
                groups.set(sessionId, {
                    sessionId,
                    messages: [],
                    startTime: conv.timestamp,
                    endTime: conv.timestamp
                });
            }

            const group = groups.get(sessionId);
            group.messages.push(conv);
            group.endTime = conv.timestamp;
        });

        // Convert to array and add titles and previews
        return Array.from(groups.values()).map(group => {
            group.title = this.generateConversationTitle(group.messages);
            group.preview = this.generateConversationPreview(group.messages);
            return group;
        }).sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
    }

    generateSessionFromTime(timestamp) {
        // Group conversations within 30 minutes of each other
        const time = new Date(timestamp);
        const roundedTime = new Date(Math.floor(time.getTime() / (30 * 60 * 1000)) * (30 * 60 * 1000));
        return roundedTime.getTime().toString();
    }

    generateConversationTitle(messages) {
        // Analyze messages to determine main theme
        const allText = messages.map(m => m.userMessage + ' ' + m.aiResponse).join(' ').toLowerCase();

        // Define topic keywords and their titles
        const topics = [
            { keywords: ['spotify', 'music', 'streaming', 'arpu'], title: 'Spotify Discussion' },
            { keywords: ['apple', 'aapl', 'iphone', 'india tariff'], title: 'Apple Analysis' },
            { keywords: ['tesla', 'tsla', 'electric', 'elon'], title: 'Tesla Insights' },
            { keywords: ['bp', 'oil', 'discovery', 'brazil'], title: 'BP Oil Discovery' },
            { keywords: ['coinbase', 'crypto', 'bitcoin', 'convertible'], title: 'Coinbase & Crypto' },
            { keywords: ['palantir', 'pltr', 'data', 'analytics'], title: 'Palantir Updates' },
            { keywords: ['rainbow', 'rare earth', 'rbw'], title: 'Rainbow Rare Earths' },
            { keywords: ['market', 'stocks', 'investment', 'portfolio'], title: 'Market Discussion' },
            { keywords: ['chat', 'collective', 'community', 'discussed'], title: 'Community Chat Review' },
            { keywords: ['valuation', 'price', 'movement', 'catalyst'], title: 'Stock Analysis' },
            { keywords: ['energy', 'nuclear', 'transition'], title: 'Energy Sector' },
            { keywords: ['s&p', 'short', 'technical', 'drawdown'], title: 'Technical Analysis' }
        ];

        // Find the best matching topic
        for (const topic of topics) {
            const matchCount = topic.keywords.filter(keyword => allText.includes(keyword)).length;
            if (matchCount >= 1) {
                return topic.title;
            }
        }

        // Default title based on first user message
        const firstMessage = messages[0]?.userMessage || '';
        if (firstMessage.length > 30) {
            return firstMessage.substring(0, 30) + '...';
        }

        return firstMessage || 'General Discussion';
    }

    generateConversationPreview(messages) {
        const firstUserMessage = messages[0]?.userMessage || '';
        return firstUserMessage.length > 80 ?
            firstUserMessage.substring(0, 80) + '...' :
            firstUserMessage;
    }

    renderConversationMessages(messages) {
        let html = '<div class="message-list">';

        messages.forEach(msg => {
            const date = new Date(msg.timestamp);
            const time = date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            html += `
                <div class="message-exchange">
                    <div class="message-time">${time}</div>
                    <div class="user-message">
                        <strong>You:</strong> ${this.escapeHtml(msg.userMessage)}
                    </div>
                    <div class="ai-message">
                        <strong>Sarah:</strong> ${this.escapeHtml(msg.aiResponse)}
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    toggleConversationDetails(sessionId) {
        const details = document.getElementById(`details-${sessionId}`);
        const icon = document.querySelector(`[data-session-id="${sessionId}"] .expand-icon i`);

        if (details.style.display === 'none') {
            details.style.display = 'block';
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-up');
        } else {
            details.style.display = 'none';
            icon.classList.remove('fa-chevron-up');
            icon.classList.add('fa-chevron-down');
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    async exportConversationHistory() {
        try {
            const response = await fetch('/api/conversation-history');
            const data = await response.json();

            if (data.success && data.conversations) {
                const exportData = {
                    exportDate: new Date().toISOString(),
                    totalConversations: data.conversations.length,
                    conversations: data.conversations
                };

                const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                    type: 'application/json'
                });

                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `conversation-history-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.updateStatus('Conversation history exported successfully!');
            }
        } catch (error) {
            console.error('Error exporting conversation history:', error);
            this.updateStatus('Error exporting conversation history.');
        }
    }

    async clearConversationHistory() {
        if (confirm('Are you sure you want to clear all conversation history? This action cannot be undone.')) {
            try {
                const response = await fetch('/api/conversation-history', {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (data.success) {
                    this.historyContent.innerHTML = '<div class="no-history">No conversation history found.</div>';
                    this.updateStatus('Conversation history cleared successfully!');
                } else {
                    this.updateStatus('Error clearing conversation history.');
                }
            } catch (error) {
                console.error('Error clearing conversation history:', error);
                this.updateStatus('Error clearing conversation history.');
            }
        }
    }

    async continueConversation(sessionId) {
        try {
            // Find the conversation group
            const conversationGroup = this.allConversations.find(group => group.sessionId === sessionId);

            if (!conversationGroup) {
                this.updateStatus('Conversation not found.');
                return;
            }

            // Load the conversation context
            this.loadConversationContext(conversationGroup);

            // Close the history modal
            this.hideHistory();
            this.hideMenu();

            // Show a message indicating the conversation has been resumed
            this.addMessageToDisplay('system', `📝 Resumed conversation: "${conversationGroup.title}"`);

            // Scroll to bottom to show the resume message
            this.scrollToBottom();

            this.updateStatus(`Conversation resumed: ${conversationGroup.title}`);

        } catch (error) {
            console.error('Error continuing conversation:', error);
            this.updateStatus('Error resuming conversation.');
        }
    }

    loadConversationContext(conversationGroup) {
        // Set the current session ID
        this.currentSessionId = conversationGroup.sessionId;

        // Load conversation history for context
        this.conversationHistory = [];
        conversationGroup.messages.forEach(msg => {
            this.conversationHistory.push(
                { role: 'user', content: msg.userMessage },
                { role: 'assistant', content: msg.aiResponse }
            );
        });

        // Keep conversation history manageable (last 20 exchanges)
        if (this.conversationHistory.length > 20) {
            this.conversationHistory = this.conversationHistory.slice(-20);
        }

        // Load any lead data from the last message
        const lastMessage = conversationGroup.messages[conversationGroup.messages.length - 1];
        if (lastMessage && lastMessage.leadData && Object.keys(lastMessage.leadData).length > 0) {
            this.leadData = { ...lastMessage.leadData };
            this.updateLeadDisplay();
        }

        // Update session ID for new messages
        this.sessionId = conversationGroup.sessionId;
    }

    addMessageToDisplay(sender, message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        if (sender === 'system') {
            messageDiv.innerHTML = `<div class="system-message">${message}</div>`;
            messageDiv.style.textAlign = 'center';
            messageDiv.style.color = '#888';
            messageDiv.style.fontStyle = 'italic';
            messageDiv.style.margin = '10px 0';
        } else {
            const senderLabel = sender === 'user' ? 'You' : 'Sarah';
            messageDiv.innerHTML = `<strong>${senderLabel}:</strong> ${message}`;
        }

        this.conversationDisplay.appendChild(messageDiv);
    }

    scrollToBottom() {
        this.conversationDisplay.scrollTop = this.conversationDisplay.scrollHeight;
    }
}

// Initialize the app when the page loads
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new VoiceLeadApp();
});

// Global function for debugging microphone button visibility
window.debugMicButton = function() {
    if (app && app.checkButtonVisibility) {
        app.checkButtonVisibility();
    } else {
        console.log('App not initialized or checkButtonVisibility method not available');
    }
};
