# AI Fallback System Documentation

## Overview

This application now includes a robust AI fallback system that automatically switches between OpenAI and Claude APIs to ensure continuous service availability. When the primary AI provider experiences issues, the system seamlessly falls back to the secondary provider while maintaining the same user experience.

## Features

### ✅ Automatic Fallback
- **Primary Provider**: OpenAI (default)
- **Fallback Provider**: <PERSON> (Anthropic)
- **Intelligent Detection**: Automatically detects when a provider is unavailable
- **Seamless Switching**: Users experience no interruption in service

### ✅ Supported Services
- **Chat Completions**: Full fallback support between OpenAI and Claude
- **Speech-to-Text**: OpenAI Whisper (graceful degradation when unavailable)
- **Text-to-Speech**: OpenAI TTS (graceful degradation when unavailable)

### ✅ Error Detection
The system detects the following conditions as "provider unavailable":
- HTTP 5xx server errors
- HTTP 429 rate limit errors
- HTTP 502/503 service unavailable
- Connection errors (ECONNREFUSED, ETIMEDOUT, etc.)
- Provider-specific overload errors

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# Claude Configuration (Fallback)
CLAUDE_API_KEY=your-claude-api-key

# AI Provider Settings
PRIMARY_AI_PROVIDER=openai          # 'openai' or 'claude'
ENABLE_AI_FALLBACK=true            # Enable/disable fallback
MOCK_MODE=false                    # Enable for testing without API calls
```

### Provider Priority

1. **Primary Provider** (set by `PRIMARY_AI_PROVIDER`)
2. **Fallback Provider** (automatically determined)
3. **Mock Mode** (if enabled, bypasses all API calls)

## API Endpoints

### Chat Completion
```bash
POST /api/chat
```
- Automatically uses fallback if primary provider fails
- Response includes `provider` field indicating which AI was used
- Includes `fallback: true` and `fallbackReason` if fallback was triggered

### AI Service Status
```bash
GET /api/ai-status
```
Returns current service configuration and status:
```json
{
  "success": true,
  "primaryProvider": "openai",
  "enableFallback": true,
  "mockMode": false,
  "lastUsedProvider": "claude",
  "fallbackReason": "OpenAI unavailable: 429 Rate limit exceeded",
  "openaiAvailable": true,
  "claudeAvailable": true
}
```

### Health Check
```bash
GET /api/ai-health?provider=openai
GET /api/ai-health?provider=claude
```
Performs a test request to check provider health:
```json
{
  "success": true,
  "status": "healthy",
  "provider": "openai"
}
```

## Model Mapping

When falling back to Claude, OpenAI models are automatically mapped:

| OpenAI Model | Claude Model |
|--------------|--------------|
| gpt-4 | claude-3-5-sonnet-20241022 |
| gpt-4-turbo | claude-3-5-sonnet-20241022 |
| gpt-3.5-turbo | claude-3-haiku-20240307 |

## Message Format Conversion

The system automatically converts between OpenAI and Claude message formats:

### OpenAI Format
```json
{
  "messages": [
    {"role": "system", "content": "You are a helpful assistant"},
    {"role": "user", "content": "Hello"}
  ]
}
```

### Claude Format (Auto-converted)
```json
{
  "system": "You are a helpful assistant",
  "messages": [
    {"role": "user", "content": "Hello"}
  ]
}
```

## Testing

### Run Fallback Tests
```bash
node test-fallback.js
```

This test script verifies:
- Service status reporting
- Health check functionality
- Chat completion with fallback
- Mock mode operation
- Error detection accuracy

### Manual Testing

1. **Test Normal Operation**:
   ```bash
   curl -X POST http://localhost:3000/api/chat \
     -H "Content-Type: application/json" \
     -d '{"message": "Hello"}'
   ```

2. **Check Service Status**:
   ```bash
   curl http://localhost:3000/api/ai-status
   ```

3. **Health Check**:
   ```bash
   curl "http://localhost:3000/api/ai-health?provider=openai"
   ```

## Error Handling

### User-Friendly Messages
The system provides clear, user-friendly error messages:

- **Both providers down**: "Our AI services are temporarily unavailable. Please try again later."
- **Rate limits**: "We're experiencing high demand. Please wait a moment and try again."
- **Speech services down**: "Voice recognition is temporarily unavailable. Please type your message instead."

### Logging
All fallback events are logged with detailed information:
```
🔄 Used fallback provider: claude, reason: OpenAI unavailable: 429 Rate limit exceeded
```

## Production Deployment

### Prerequisites
1. Valid OpenAI API key with sufficient quota
2. Valid Claude API key
3. Both environment variables properly configured

### Monitoring
Monitor these endpoints for service health:
- `/api/ai-status` - Overall service status
- `/api/ai-health?provider=openai` - OpenAI health
- `/api/ai-health?provider=claude` - Claude health

### Alerts
Set up alerts for:
- Fallback activation (check logs for "Used fallback provider")
- Both providers unhealthy
- High error rates on AI endpoints

## Limitations

### Speech Services
- **Speech-to-Text**: Only available through OpenAI Whisper
- **Text-to-Speech**: Only available through OpenAI TTS
- When OpenAI is down, these services gracefully degrade with helpful error messages

### Future Enhancements
- Integration with additional speech service providers
- Automatic retry mechanisms
- Circuit breaker patterns
- Provider performance monitoring

## Troubleshooting

### Common Issues

1. **"No AI providers available"**
   - Check API keys in `.env` file
   - Verify network connectivity
   - Check provider status pages

2. **Fallback not working**
   - Ensure `ENABLE_AI_FALLBACK=true`
   - Check that both API keys are valid
   - Verify error is detected as "unavailable"

3. **Speech services failing**
   - Check OpenAI API key and quota
   - Verify audio file format compatibility
   - Check network connectivity to OpenAI

### Debug Mode
Enable detailed logging by setting:
```bash
NODE_ENV=development
```

This provides additional console output for debugging fallback behavior.
