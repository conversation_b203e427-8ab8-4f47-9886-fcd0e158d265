<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Preview Card Test</title>
    <link rel="stylesheet" href="public/styles.css">
    <style>
        body {
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }
        
        .test-button {
            background: #00d4aa;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            margin: 10px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .test-button:hover {
            background: #00b894;
        }
        
        .test-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }
        
        .test-info h3 {
            color: #00d4aa;
            margin-bottom: 10px;
        }
        
        .test-info p {
            margin: 8px 0;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📊 Stock Preview Card Test</h1>
        
        <div class="test-info">
            <h3>Test the Stock Preview Component</h3>
            <p>Click the buttons below to test different stock preview cards.</p>
            <p>The card should appear with a smooth fade-in animation from the bottom.</p>
        </div>
        
        <div class="test-buttons">
            <button class="test-button" onclick="showApple()">Show Apple (AAPL)</button>
            <button class="test-button" onclick="showTesla()">Show Tesla (TSLA)</button>
            <button class="test-button" onclick="showNvidia()">Show NVIDIA (NVDA)</button>
            <button class="test-button" onclick="showSpotify()">Show Spotify (SPOT)</button>
            <button class="test-button" onclick="hidePreview()">Hide Preview</button>
        </div>
        
        <div class="test-info">
            <h3>Expected Behavior</h3>
            <p><strong>Show:</strong> Card slides up from bottom with fade-in</p>
            <p><strong>Hide:</strong> Card slides down with fade-out</p>
            <p><strong>Auto-hide:</strong> Card disappears after 4 seconds</p>
            <p><strong>Position:</strong> Centered horizontally, above status indicator</p>
        </div>
        
        <div class="test-info">
            <h3>Design Specifications</h3>
            <p><strong>Style:</strong> Minimal, clean, non-intrusive</p>
            <p><strong>Content:</strong> Company name + ticker symbol only</p>
            <p><strong>Background:</strong> Semi-transparent dark with blur</p>
            <p><strong>Border:</strong> Subtle teal accent</p>
            <p><strong>Typography:</strong> Clean, readable fonts</p>
        </div>
    </div>

    <!-- Stock Preview Card (copied from main app) -->
    <div class="stock-preview-card" id="stockPreviewCard" style="display: none;">
        <div class="stock-preview-content">
            <div class="stock-header">
                <span class="company-name" id="previewCompanyName">Apple Inc.</span>
                <span class="ticker-symbol" id="previewTicker">AAPL</span>
            </div>
            <div class="stock-metrics">
                <div class="price-info">
                    <span class="current-price" id="previewPrice">$189.84</span>
                    <span class="price-change" id="previewChange">+2.34%</span>
                </div>
                <div class="additional-info">
                    <span class="market-cap" id="previewMarketCap">$2.89T</span>
                    <span class="price-target" id="previewTarget">PT: $220</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Status indicator for reference positioning -->
    <div class="status-indicator" style="opacity: 0.3;">
        <span class="status-text">Status indicator (for positioning reference)</span>
    </div>

    <script>
        // Enhanced test functions for the stock preview card
        const stockPreviewCard = document.getElementById('stockPreviewCard');
        const previewCompanyName = document.getElementById('previewCompanyName');
        const previewTicker = document.getElementById('previewTicker');
        const previewPrice = document.getElementById('previewPrice');
        const previewChange = document.getElementById('previewChange');
        const previewMarketCap = document.getElementById('previewMarketCap');
        const previewTarget = document.getElementById('previewTarget');

        let hideTimeout;

        function showStockPreview(stockData) {
            // Clear any existing hide timeout
            if (hideTimeout) {
                clearTimeout(hideTimeout);
            }

            // Update basic info
            previewCompanyName.textContent = stockData.companyName;
            previewTicker.textContent = stockData.ticker;

            // Update price info
            previewPrice.textContent = `$${stockData.price}`;

            // Update price change with color coding
            const changePercent = stockData.changePercent;
            const changeText = changePercent > 0 ? `+${changePercent}%` : `${changePercent}%`;
            previewChange.textContent = changeText;

            // Add appropriate class for color
            previewChange.classList.remove('positive', 'negative');
            previewChange.classList.add(changePercent >= 0 ? 'positive' : 'negative');

            // Update market cap and price target
            previewMarketCap.textContent = stockData.marketCap;
            previewTarget.textContent = `PT: $${stockData.priceTarget}`;

            // Show the card with animation
            stockPreviewCard.style.display = 'block';

            // Force reflow
            stockPreviewCard.offsetHeight;

            // Add show class for smooth animation
            stockPreviewCard.classList.add('show', 'animate-in');
            stockPreviewCard.classList.remove('animate-out');

            console.log(`📊 Showing stock preview: ${stockData.companyName} (${stockData.ticker}) - $${stockData.price} (${changeText})`);

            // Auto-hide after 4 seconds
            hideTimeout = setTimeout(() => {
                hideStockPreview();
            }, 4000);
        }

        function hideStockPreview() {
            if (hideTimeout) {
                clearTimeout(hideTimeout);
            }

            // Add exit animation
            stockPreviewCard.classList.add('animate-out');
            stockPreviewCard.classList.remove('show', 'animate-in');

            // Hide after animation completes
            setTimeout(() => {
                stockPreviewCard.style.display = 'none';
                stockPreviewCard.classList.remove('animate-out');
            }, 300);

            console.log('📊 Hiding stock preview');
        }

        // Test functions with full stock data
        function showApple() {
            showStockPreview({
                ticker: 'AAPL',
                companyName: 'Apple Inc.',
                price: 189.84,
                changePercent: 2.34,
                marketCap: '2.89T',
                priceTarget: 220.00
            });
        }

        function showTesla() {
            showStockPreview({
                ticker: 'TSLA',
                companyName: 'Tesla, Inc.',
                price: 248.50,
                changePercent: -1.87,
                marketCap: '789.2B',
                priceTarget: 300.00
            });
        }

        function showNvidia() {
            showStockPreview({
                ticker: 'NVDA',
                companyName: 'NVIDIA Corporation',
                price: 118.75,
                changePercent: 3.45,
                marketCap: '2.92T',
                priceTarget: 140.00
            });
        }

        function showSpotify() {
            showStockPreview({
                ticker: 'SPOT',
                companyName: 'Spotify Technology S.A.',
                price: 342.18,
                changePercent: 1.23,
                marketCap: '68.5B',
                priceTarget: 380.00
            });
        }

        function hidePreview() {
            hideStockPreview();
        }

        // Demo sequence on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🎬 Starting enhanced demo sequence...');
                showApple();

                setTimeout(() => {
                    showTesla();

                    setTimeout(() => {
                        showNvidia();

                        setTimeout(() => {
                            showSpotify();

                            setTimeout(() => {
                                hideStockPreview();
                            }, 3000);
                        }, 3000);
                    }, 3000);
                }, 3000);
            }, 2000);
        });
    </script>
</body>
</html>
