<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Preview Card Test</title>
    <link rel="stylesheet" href="public/styles.css">
    <style>
        body {
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }
        
        .test-button {
            background: #00d4aa;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            margin: 10px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .test-button:hover {
            background: #00b894;
        }
        
        .test-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }
        
        .test-info h3 {
            color: #00d4aa;
            margin-bottom: 10px;
        }
        
        .test-info p {
            margin: 8px 0;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📊 Stock Preview Card Test</h1>
        
        <div class="test-info">
            <h3>Test the Stock Preview Component</h3>
            <p>Click the buttons below to test different stock preview cards.</p>
            <p>The card should appear with a smooth fade-in animation from the bottom.</p>
        </div>
        
        <div class="test-buttons">
            <button class="test-button" onclick="showApple()">Show Apple (AAPL)</button>
            <button class="test-button" onclick="showTesla()">Show Tesla (TSLA)</button>
            <button class="test-button" onclick="showNvidia()">Show NVIDIA (NVDA)</button>
            <button class="test-button" onclick="showSpotify()">Show Spotify (SPOT)</button>
            <button class="test-button" onclick="hidePreview()">Hide Preview</button>
        </div>
        
        <div class="test-info">
            <h3>Expected Behavior</h3>
            <p><strong>Show:</strong> Card slides up from bottom with fade-in</p>
            <p><strong>Hide:</strong> Card slides down with fade-out</p>
            <p><strong>Auto-hide:</strong> Card disappears after 4 seconds</p>
            <p><strong>Position:</strong> Centered horizontally, above status indicator</p>
        </div>
        
        <div class="test-info">
            <h3>Design Specifications</h3>
            <p><strong>Style:</strong> Minimal, clean, non-intrusive</p>
            <p><strong>Content:</strong> Company name + ticker symbol only</p>
            <p><strong>Background:</strong> Semi-transparent dark with blur</p>
            <p><strong>Border:</strong> Subtle teal accent</p>
            <p><strong>Typography:</strong> Clean, readable fonts</p>
        </div>
    </div>

    <!-- Stock Preview Card (copied from main app) -->
    <div class="stock-preview-card" id="stockPreviewCard" style="display: none;">
        <div class="stock-preview-content">
            <div class="stock-info">
                <span class="company-name" id="previewCompanyName">Apple Inc.</span>
                <span class="ticker-symbol" id="previewTicker">AAPL</span>
            </div>
        </div>
    </div>

    <!-- Status indicator for reference positioning -->
    <div class="status-indicator" style="opacity: 0.3;">
        <span class="status-text">Status indicator (for positioning reference)</span>
    </div>

    <script>
        // Simple test functions for the stock preview card
        const stockPreviewCard = document.getElementById('stockPreviewCard');
        const previewCompanyName = document.getElementById('previewCompanyName');
        const previewTicker = document.getElementById('previewTicker');
        
        let hideTimeout;

        function showStockPreview(companyName, ticker) {
            // Clear any existing hide timeout
            if (hideTimeout) {
                clearTimeout(hideTimeout);
            }

            // Update content
            previewCompanyName.textContent = companyName;
            previewTicker.textContent = ticker;

            // Show the card with animation
            stockPreviewCard.style.display = 'block';
            
            // Force reflow
            stockPreviewCard.offsetHeight;
            
            // Add show class for smooth animation
            stockPreviewCard.classList.add('show', 'animate-in');
            stockPreviewCard.classList.remove('animate-out');

            console.log(`📊 Showing stock preview: ${companyName} (${ticker})`);
            
            // Auto-hide after 4 seconds
            hideTimeout = setTimeout(() => {
                hideStockPreview();
            }, 4000);
        }

        function hideStockPreview() {
            if (hideTimeout) {
                clearTimeout(hideTimeout);
            }

            // Add exit animation
            stockPreviewCard.classList.add('animate-out');
            stockPreviewCard.classList.remove('show', 'animate-in');

            // Hide after animation completes
            setTimeout(() => {
                stockPreviewCard.style.display = 'none';
                stockPreviewCard.classList.remove('animate-out');
            }, 300);

            console.log('📊 Hiding stock preview');
        }

        // Test functions
        function showApple() {
            showStockPreview('Apple Inc.', 'AAPL');
        }

        function showTesla() {
            showStockPreview('Tesla, Inc.', 'TSLA');
        }

        function showNvidia() {
            showStockPreview('NVIDIA Corporation', 'NVDA');
        }

        function showSpotify() {
            showStockPreview('Spotify Technology S.A.', 'SPOT');
        }

        function hidePreview() {
            hideStockPreview();
        }

        // Demo sequence on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🎬 Starting demo sequence...');
                showApple();
                
                setTimeout(() => {
                    showTesla();
                    
                    setTimeout(() => {
                        showNvidia();
                        
                        setTimeout(() => {
                            hideStockPreview();
                        }, 3000);
                    }, 3000);
                }, 3000);
            }, 2000);
        });
    </script>
</body>
</html>
